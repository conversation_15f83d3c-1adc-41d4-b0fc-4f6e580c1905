<template>
  <StatusTooltip :sections="sections" />
</template>

<script setup lang="ts">
import type { StatusInfo } from '@/types/statuses'
import { computed } from 'vue'
import StatusTooltip from '@/components/statuses/StatusTooltip.vue'
import { formatDateTimeISO } from '@/libs/temporal'

const props = defineProps<{ statusInfo: StatusInfo }>()

type Row = { label: string; value: string | number | null }
type Section = Row[]

const sections = computed(() => {
  const { scrapeStatus, errorType, data } = props.statusInfo
  const sections: Section[] = [
    [
      { label: 'Scrape', value: scrapeStatus },
      { label: 'Error', value: errorType || null },
    ],
  ]

  if (data) {
    sections.push([
      { label: 'Organization', value: data.organizationName || null },
      { label: 'Source', value: data.source || null },
      { label: 'Created', value: formatDateTimeISO(data.createdAt) },
      { label: 'Updated', value: formatDateTimeISO(data.updatedAt) },
      { label: 'Failed Scrapes', value: data.consecutiveFailedScrapeCount },
      { label: 'Last Success', value: formatDateTimeISO(data.lastSuccessTimestamp) },
      { label: 'Last Fail', value: formatDateTimeISO(data.lastFailTimestamp) },
      { label: 'Fail Reason', value: data.lastFailReason || null },
      { label: 'Last User', value: data.userEmail || null },
      { label: 'Last Operation ID', value: data.lastOperationId || null },
    ])

    if (data.reports.length == 1) {
      sections.push([
        { label: 'Report', value: data.reports[0].state },
        {
          label: 'Date Range',
          value: `${data.reports[0].date_from} - ${data.reports[0].date_to}`,
        },
      ])
    } else if (data.reports.length > 1) {
      const converted = data.reports.filter(({ state }) => state === 'CONVERTED').length
      const pending = data.reports.filter(({ state }) => state === 'PENDING').length
      const notUploaded = data.reports.filter(({ state }) => state === 'NOT_UPLOADED_YET').length

      sections.push([
        { label: 'Reports', value: data.reports.length },
        { label: 'Converted', value: converted },
        { label: 'Pending', value: pending },
        { label: 'Not Uploaded Yet', value: notUploaded },
      ])
    }
  }

  return sections
})
</script>
