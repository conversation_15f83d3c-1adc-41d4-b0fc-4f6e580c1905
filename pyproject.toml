[tool.poetry]
name = "dataoffice"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poe.tasks]
test = ["test-unit", "test-integration"]
lint = "ruff check --fix ."
format = "ruff format"
check = ["lint", "format", "test"]
start = "uvicorn dataoffice.main:app --reload --port 8666 --host localhost"
hooks = "poetry run pre-commit install"
test-unit = ["_test_unit", "_check_unit_dead_fixtures"]
test-integration = ["_test_integration", "_check_integration_dead_fixtures"]
_test_unit = "pytest tests/unit -vvv --random-order --color=yes -n auto"
_test_integration = "pytest tests/integration -vvv --random-order --color=yes -n auto"
_check_unit_dead_fixtures = "pytest tests/unit --dead-fixtures"
_check_integration_dead_fixtures = "pytest tests/integration --dead-fixtures"
ci-test-unit = ["_test_unit", "_check_unit_dead_fixtures"]
ci-test-integration = ["_test_integration", "_check_integration_dead_fixtures"]

[tool.poe.executor]
type = "poetry"

[tool.poetry.dependencies]
python = "^3.12.4"
fastapi = "^0.110.1"
requests = "^2.31.0"
rich = "^13.7.1"
uvicorn = "^0.29.0"
pydantic = "^2.6.4"
pydantic-settings = "^2.2.1"
jinja2 = "^3.1.3"
msal = "^1.28.0"
itsdangerous = "^2.2.0"
httpx = "^0.27.0"
arrow = "^1.3.0"
pyhumps = "^3.8.0"
psutil = "^7.0.0"


[tool.poetry.group.dev.dependencies]
ruff = "^0.3.5"
djlint = "^1.34.1"
pytest = "^8.2.2"
respx = "^0.21.1"
pyright = "^1.1.367"
poethepoet = "^0.31.1"
pytest-random-order = "^1.1.1"
pytest-xdist = "^3.6.1"
pytest-deadfixtures = "^2.2.1"
pre-commit = "^4.0.1"
pytest-mock = "^3.12.0"
pytest-asyncio = "^0.23.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
src = ["dataoffice", "tests"]
exclude = ["frontend/public/theme/", "static/theme/"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
    # unused suppression comments https://docs.astral.sh/ruff/linter/#detecting-unused-suppression-comments
    "RUF100",
]
ignore = [
    "E501", # Line too long
]

[tool.pyright]
typeCheckingMode = "standard"

[tool.pytest.ini_options]
filterwarnings = ["ignore::DeprecationWarning:arrow:"]
