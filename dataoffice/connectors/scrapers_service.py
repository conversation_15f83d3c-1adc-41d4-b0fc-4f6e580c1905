from datetime import datetime
from enum import StrEnum

import httpx
from dataoffice.connectors.common import ServiceHealth, check_service_health, get_model
from dataoffice.settings import settings
from pydantic import BaseModel

_client = httpx.AsyncClient(
    base_url=settings().scraper_service_url,
    headers={"x-api-key": settings().scraper_service_key},
    timeout=settings().default_connection_timeout,
)


class ScraperBinaryStatus(StrEnum):
    CONFIGURED = "CONFIGURED"
    DELETED = "DELETED"
    DISABLED = "DISABLED"
    FAILED = "FAILED"  # scraper failed with some error
    FINISHED = "FINISHED"  # scraper finished with success
    MANUALLY_BLOCKED = "MANUALLY_BLOCKED"
    MANUALLY_UNBLOCKED = "MANUALLY_UNBLOCKED"
    SCHEDULED = "SCHEDULED"  # scraper was scheduled to run
    STARTED = "STARTED"  # scraper was started (manually or automatically)
    STOPPED = "STOPPED"  # scraper was stopped (manually)
    UNCONFIGURED = "UNCONFIGURED"

    def __str__(self):
        return self.value


sourceNameMapping = {
    "microsoft_sales": "Microsoft",
    "humble_sales": "Humble",
    "gog_sales": "GOG",
    "nintendo_sales": "Nintendo sales",
    "nintendo_discounts": "Nintendo discounts",
    "nintendo_wishlist_actions": "Nintendo wishlists",
    "nintendo_wii_ds_sales": "Nintendo Wii/DS",
    "nintendo_preorders": "Nintendo pre-orders",
    "nintendo_free_to_play": "Nintendo F2P",
    "epic_sales": "Epic Games Store",
    "steam_sales": "Steam sales",
    "steam_wishlists": "Steam wishlists",
    "steam_impressions": "Steam visibility",
    "steam_discounts": "Steam discounts",
    "steam_in_app_sales": "Steam in-app sales",
    "steam_wishlist_country": "Steam country wishlist",
    "steam_bundles": "Steam bundles",
    "meta_rift_sales": "Meta Rift",
    "meta_quest_sales": "Meta Quest",
    "playstation_sales": "PlayStation sales",
    "playstation_wishlist_actions": "PlayStation wishlists",
    "app_store_sales": "App Store",
    "google_sales": "Google Play",
}


class DateRange(BaseModel):
    date_from: str
    date_to: str
    days_in_range: int


class ScraperStatusProjection(BaseModel):
    organization_id: str
    source: str
    state: ScraperBinaryStatus
    created_at: datetime
    updated_at: datetime
    consecutive_failed_scrape_count: int
    last_success_timestamp: datetime | None
    last_fail_timestamp: datetime | None
    last_fail_reason: str | None
    user_id: str
    last_origin: str | None
    last_operation_id: str | None
    last_report_ids: list[int] | None
    last_scrape_date_ranges: list[DateRange] | None = []
    version: int


class DataTableResponse(BaseModel):
    data: list[ScraperStatusProjection]
    draw: int
    length: int
    recordsFiltered: int
    recordsTotal: int
    start: int


async def get_scraper_statuses() -> list[ScraperStatusProjection]:
    return await get_model(
        _client,
        list[ScraperStatusProjection],
        "/scraper_state/all",
    )


async def get_organization_scraper_statuses(
    organization_id,
) -> list[ScraperStatusProjection]:
    return await get_model(
        _client,
        list[ScraperStatusProjection],
        "/scraper_state/all",
        params={"organization_id": organization_id},
    )


async def block_scraper_status(organization_id: str, user_id: str, source: str):
    response = await _client.post(
        "/scraper/block",
        json={
            "organization_id": organization_id,
            "user_id": user_id,
            "source": source,
        },
    )
    response.raise_for_status()


async def get_scraper_statuses_datatable(query: str):
    response = await _client.get(
        "/scraper_state/datatable",
        params=query,
    )
    response.raise_for_status()
    return response.json()


async def unblock_scraper_status(organization_id: str, user_id: str, source: str):
    response = await _client.post(
        "/scraper/unblock",
        json={
            "organization_id": organization_id,
            "user_id": user_id,
            "source": source,
        },
    )
    response.raise_for_status()


class ScraperOperationHistory(BaseModel):
    organization_id: str
    source: str
    operation_id: str
    state: str
    created_at: datetime
    updated_at: datetime
    start_timestamp: datetime | None
    end_timestamp: datetime | None
    fail_reason: str | None
    execution_time: datetime | None = None


async def get_scraper_operation_history(
    organization_id: str | None = None, source: str | None = None
) -> list[ScraperOperationHistory]:
    return await get_model(
        _client,
        list[ScraperOperationHistory],
        "/scraper_operation_history",
        params={"organization_id": organization_id, "source": source},
    )


async def get_scraper_operation_history_datatable(query: str):
    response = await _client.get(
        "/scraper_operation_history/datatable",
        params=query,
    )
    response.raise_for_status()
    return response.json()


async def check_health() -> ServiceHealth:
    return await check_service_health(_client)
