from datetime import datetime

import httpx
from dataoffice.connectors.common import (
    PaginatedResponse,
    ServiceHealth,
    check_service_health,
    get_model,
)
from dataoffice.settings import settings
from pydantic import BaseModel

_client = httpx.AsyncClient(
    base_url=settings().user_service_url,
    headers={"x-api-key": settings().user_service_key},
    timeout=settings().default_connection_timeout,
)


class Organization(BaseModel):
    id: str
    name: str
    legacy_id: int | None
    created_at: datetime


class User(BaseModel):
    email: str
    first_name: str | None
    last_name: str | None
    company_name: str | None
    id: str
    legacy_id: int | None
    verified: bool
    created_at: datetime

    @property
    def exists(self) -> bool:
        return self.id != "deleted"

    @classmethod
    def get_deleted_user(cls):
        return cls(
            email="",
            first_name="deleted",
            last_name="user",
            company_name="<DELETED>",
            id="deleted",
            legacy_id=0,
            verified=False,
            created_at=datetime.now(),
        )


async def search_users(search_substring: str, offset: int, limit: int):
    params = {"search_substring": search_substring, "offset": offset, "limit": limit}
    results_page = await get_model(
        _client, PaginatedResponse[User], "/user/search", params
    )
    return results_page.data


async def search_organizations(search_substring: str, offset: int, limit: int):
    params = {"search_substring": search_substring, "offset": offset, "limit": limit}
    results_page = await get_model(
        _client, PaginatedResponse[Organization], "/organization/search", params
    )
    return results_page.data


async def get_user(user_id: str) -> User:
    try:
        return await get_model(_client, User, f"/user/{user_id}")
    except httpx.HTTPStatusError as e:
        if e.response is not None and e.response.status_code == 404:
            return User.get_deleted_user()
        else:
            raise e


class Permission(BaseModel):
    organization_id: str
    name: str
    filters: list


async def get_organization_id_for_user(user_id: str) -> str | None:
    all_user_permissions: list[Permission] = await get_model(
        client=_client,
        model=list[Permission],
        path=f"/user/{user_id}/permissions",
    )
    organization_ids = [
        permission.organization_id
        for permission in all_user_permissions
        if permission.name == "USER_SERVICE_V2_ASSIGN_PERMISSIONS"
    ]

    if len(organization_ids) == 1:
        return organization_ids[0]

    return None


async def check_health() -> ServiceHealth:
    return await check_service_health(_client)
