import datetime

import pytest
from dataoffice.connectors.scrapers_service import (
    ScraperBinaryStatus,
    get_scraper_operation_history,
    get_scraper_statuses,
)

fromISO = datetime.datetime.fromisoformat


@pytest.mark.asyncio(scope="session")
async def test_get_scraper_statuses(test_organization_id, test_user_id):
    statuses = await get_scraper_statuses()
    scp_test_account_statuses = [
        s.model_dump() for s in statuses if s.organization_id == test_organization_id
    ]
    assert len(scp_test_account_statuses) == 2

    assert scp_test_account_statuses == [
        {
            "consecutive_failed_scrape_count": 0,
            "created_at": fromISO("2025-04-18T11:28:21.399328+00:00"),
            "last_fail_reason": None,
            "last_fail_timestamp": None,
            "last_operation_id": "5fe964f9-ed98-4e36-86d8-dec0ef92d079",
            "last_origin": "ScraperLib_1.60.0",
            "last_success_timestamp": fromISO("2025-05-03T19:37:37.773156+00:00"),
            "last_report_ids": None,
            "last_scrape_date_ranges": None,
            "user_id": test_user_id,
            "organization_id": test_organization_id,
            "source": "playstation_sales",
            "state": ScraperBinaryStatus.FINISHED,
            "updated_at": fromISO("2025-05-03T19:37:37.773156+00:00"),
            "version": 0,
        },
        {
            "consecutive_failed_scrape_count": 0,
            "created_at": fromISO("2025-04-18T11:28:21.399328+00:00"),
            "last_fail_reason": None,
            "last_fail_timestamp": None,
            "last_operation_id": "bae5fee6-ce0a-4969-a3b2-a2e07a543c7e",
            "last_origin": "ScraperLib_1.60.0",
            "last_success_timestamp": fromISO("2025-05-03T19:46:38.173875+00:00"),
            "last_report_ids": None,
            "last_scrape_date_ranges": None,
            "user_id": test_user_id,
            "organization_id": test_organization_id,
            "source": "playstation_wishlist_actions",
            "state": ScraperBinaryStatus.FINISHED,
            "updated_at": fromISO("2025-05-03T19:46:38.173875+00:00"),
            "version": 0,
        },
    ]


@pytest.mark.asyncio(scope="session")
async def test_get_scraper_operation_history(test_organization_id):
    history = await get_scraper_operation_history(
        organization_id=test_organization_id,
    )
    assert len(history) == 2

    history_dumps = [h.model_dump() for h in history]
    assert history_dumps == [
        {
            "organization_id": test_organization_id,
            "source": "playstation_wishlist_actions",
            "operation_id": "bae5fee6-ce0a-4969-a3b2-a2e07a543c7e",
            "state": "FINISHED",
            "created_at": fromISO("2025-04-18T11:28:21.406781+00:00"),
            "updated_at": fromISO("2025-05-03T19:46:38.173875+00:00"),
            "start_timestamp": fromISO("2025-05-03T19:37:38.132754+00:00"),
            "end_timestamp": fromISO("2025-05-03T19:46:38.173875+00:00"),
            "fail_reason": None,
            "execution_time": None,
        },
        {
            "organization_id": test_organization_id,
            "source": "playstation_sales",
            "operation_id": "5fe964f9-ed98-4e36-86d8-dec0ef92d079",
            "state": "FINISHED",
            "created_at": fromISO("2025-04-18T11:28:21.406781+00:00"),
            "updated_at": fromISO("2025-05-03T19:37:37.773156+00:00"),
            "start_timestamp": fromISO("2025-05-03T19:27:45.826515+00:00"),
            "end_timestamp": fromISO("2025-05-03T19:37:37.773156+00:00"),
            "fail_reason": None,
            "execution_time": None,
        },
    ]
