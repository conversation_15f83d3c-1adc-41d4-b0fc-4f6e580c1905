<template>
  <div
    v-if="reports.length > 0 && atLeastOneReportIsUploaded"
    class="status-ring"
    :class="{ pulse: shouldPulse }"
  >
    <svg :viewBox="`0 0 ${viewportSize} ${viewportSize}`" class="ring-svg">
      <g
        :transform="`translate(${(viewportSize - ringSize) / 2}, ${(viewportSize - ringSize) / 2})`"
      >
        <circle
          v-for="(report, index) in reports"
          :key="`${report.date_from}-${report.date_to}`"
          class="ring-segment"
          :class="report.state.toLowerCase()"
          :cx="ringSize / 2"
          :cy="ringSize / 2"
          :r="radius"
          fill="none"
          :stroke-width="strokeWidth"
          :stroke-dasharray="`${segmentLength} ${circumference - segmentLength}`"
          :stroke-dashoffset="getSegmentOffset(index)"
        />
        <circle
          v-if="!areAllReportsConvertedOrPending"
          class="border"
          :cx="ringSize / 2"
          :cy="ringSize / 2"
          :r="outerBorderRadius"
          fill="none"
          :stroke-width="outerBorderStrokeWidth"
        />
        <circle
          v-if="!areAllReportsConvertedOrPending"
          class="border"
          :cx="ringSize / 2"
          :cy="ringSize / 2"
          :r="innerBorderRadius"
          fill="none"
          :stroke-width="innerBorderStrokeWidth"
        />
      </g>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Report, ReportPlaceholder } from '@/api/types'

const viewportSize = 50
const ringSize = 44
const strokeWidth = 8
const radius = ringSize / 2 - strokeWidth / 2
const circumference = 2 * Math.PI * radius

const outerBorderStrokeWidth = 1.5
const outerBorderRadius = radius + strokeWidth / 2
const innerBorderStrokeWidth = 1.5
const innerBorderRadius = radius - strokeWidth / 2

const props = defineProps<{
  reports: Array<Report | ReportPlaceholder>
  shouldPulse: boolean
}>()
const segmentLength = computed(() => {
  return circumference / props.reports.length
})

const areAllReportsConvertedOrPending = computed(() => {
  return props.reports.every((report) => report.state === 'CONVERTED' || report.state === 'PENDING')
})

const atLeastOneReportIsUploaded = computed(() => {
  return props.reports.some((report) => report.state !== 'NOT_UPLOADED_YET')
})

const getSegmentOffset = (index: number) => {
  return -index * segmentLength.value
}
</script>

<style scoped>
[data-bs-theme='light'] .border {
  stroke: var(--falcon-body-color);
}

[data-bs-theme='dark'] .border {
  stroke: var(--falcon-body-color);
}

.status-ring {
  position: absolute;
  z-index: var(--z-index-status-ring);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.live-refresh .status-ring.pulse {
  animation: pulse 2s infinite;
}

.ring-svg {
  position: absolute;
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  z-index: var(--z-index-status-background);
}

.ring-segment {
  stroke: #ffa500;
}

.status-ring .ring-segment.converted {
  stroke: #36b37e;
}

.status-ring .ring-segment.deleted {
  stroke: #343434;
}

.status-ring .ring-segment.failed {
  stroke: #e63757;
}

.status-ring .ring-segment.pending {
  stroke: #ffa500;
}

/* stylelint-disable-next-line selector-class-pattern */
.status-ring .ring-segment.not_uploaded_yet {
  stroke: var(--falcon-body-bg);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}
</style>
