<script setup lang="ts">
import { RouterView } from 'vue-router'
import LoginLinks from '@/components/LoginLinks.vue'
import HealthMonitor from '@/components/HealthMonitor.vue'
import NotificationCenter from '@/components/NotificationCenter.vue'
</script>

<template>
  <HealthMonitor />
  <nav class="navbar navbar-light navbar-vertical navbar-expand-xl navbar-card">
    <div class="logo-container d-flex align-items-center">
      <a class="navbar-brand" href="/">
        <div class="text-end py-2">
          <img
            class="me-2 dataoffice-logo"
            src="/static/logo-light_local.png"
            height="50"
            alt="dataoffice"
          />
          <div class="logo-env-tag">
            <span class="env-tag"></span>&nbsp;
            <span class="vue">via Vue</span>
          </div>
        </div>
      </a>
    </div>

    <div
      class="collapse navbar-collapse"
      id="navbarVerticalCollapse"
      style="position: relative; margin-top: 0"
    >
      <!-- <button
        class="btn navbar-toggler-humburger-icon navbar-vertical-toggle"
        data-bs-toggle="tooltip"
        data-bs-placement="left"
        title="Toggle Navigation"
      >
        <span class="navbar-toggle-icon"><span class="toggle-line"></span></span>
      </button> -->
      <div class="navbar-vertical-content scrollbar">
        <ul class="navbar-nav flex-column mb-3" id="navbarVerticalNav">
          <li class="nav-item">
            <!-- label-->

            <a class="nav-link" href="/" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fas fa-home"></span></span>
                <span class="nav-link-text ps-1"> Home </span>
              </div>
            </a>
          </li>

          <li class="nav-item">
            <!-- label-->

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Client Issues</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <a class="nav-link" href="/data-health" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fas fa-heartbeat"></span></span>
                <span class="nav-link-text ps-1"> Data Health </span>
              </div>
            </a>
          </li>

          <li class="nav-item">
            <!-- label-->

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Scrapers</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <a class="nav-link" href="/cloud-reports" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fas fa-cloud"></span></span>
                <span class="nav-link-text ps-1"> Cloud Reports </span>
              </div>
            </a>

            <a class="nav-link" href="/scraper_statuses" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fas fa-star"></span></span>
                <span class="nav-link-text ps-1"> Statuses </span>
              </div>
            </a>

            <a class="nav-link" href="/scraper_operation_history" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fas fa-clock"></span></span>
                <span class="nav-link-text ps-1"> Operation History </span>
              </div>
            </a>

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Status Grids</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <RouterLink to="/clients-statuses" class="nav-link" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="text-900 fs-9 bi-grid-3x3"></span></span>
                <span class="nav-link-text ps-1"> All Clients</span>
              </div>
            </RouterLink>

            <RouterLink to="/managed-partners-statuses" class="nav-link" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="text-900 fs-9 bi-grid-3x3"></span></span>
                <span class="nav-link-text ps-1"> Managed Partners</span>
              </div>
            </RouterLink>

            <RouterLink to="/managed-partners-monthly-coverage" class="nav-link" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="text-900 fs-9 bi-grid-3x3"></span></span>
                <span class="nav-link-text ps-1"> Monthly Coverage</span>
              </div>
            </RouterLink>
          </li>

          <li class="nav-item">
            <!-- label-->

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Monitoring</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <a class="nav-link" href="https://grafana.indiebi.dev/" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-chart-line text-info"></span></span>
                <span class="nav-link-text ps-1">
                  Grafana
                  <small>DEV cluster</small>
                </span>
              </div>
            </a>

            <a class="nav-link" href="https://grafana.indiebi.com/" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"
                  ><span class="fa fa-chart-line text-danger"></span
                ></span>
                <span class="nav-link-text ps-1">
                  Grafana
                  <small>PROD cluster</small>
                </span>
              </div>
            </a>

            <a
              class="nav-link"
              href="https://app-grafana-core-indiebi.azurewebsites.net/"
              role="button"
            >
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-chart-area"></span></span>
                <span class="nav-link-text ps-1">
                  Grafana
                  <small>CORE</small>
                </span>
              </div>
            </a>

            <a
              class="nav-link"
              href="https://indiebi.kb.westeurope.azure.elastic-cloud.com:9243"
              role="button"
            >
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-wave-square"></span></span>
                <span class="nav-link-text ps-1"> Elastic/Kibana </span>
              </div>
            </a>

            <a
              class="nav-link"
              href="https://indiebi.kb.westeurope.azure.elastic-cloud.com:9243/s/dpt/app/r/s/Y5inf"
              role="button"
            >
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-chart-pie"></span></span>
                <span class="nav-link-text ps-1">
                  Elastic
                  <small>PowerBI Refresh Dashboard</small>
                </span>
              </div>
            </a>
          </li>

          <li class="nav-item">
            <!-- label-->

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Useful links</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <a class="nav-link" href="https://backoffice.indiebi.dev" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-link text-info"></span></span>
                <span class="nav-link-text ps-1">
                  Backoffice
                  <small>DEV</small>
                </span>
              </div>
            </a>

            <a class="nav-link" href="https://backoffice.indiebi.com" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-link text-danger"></span></span>
                <span class="nav-link-text ps-1">
                  Backoffice
                  <small>PROD</small>
                </span>
              </div>
            </a>

            <a
              class="nav-link"
              href="https://indiebi.atlassian.net/wiki/spaces/DPT/pages/478216194/Data+Platform+Team+Intro"
              role="button"
            >
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-link"></span></span>
                <span class="nav-link-text ps-1"> DPT Confluence </span>
              </div>
            </a>

            <a
              class="nav-link"
              href="https://bluebrick.gitlab.io/indiebi/data-platform-team/docs/"
              role="button"
            >
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-link"></span></span>
                <span class="nav-link-text ps-1"> DPT Dev Docs </span>
              </div>
            </a>
            <LoginLinks />
          </li>

          <li class="nav-item">
            <!-- label-->

            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label">Sample pages</div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>

            <a class="nav-link" href="theme/index.html" role="button">
              <div class="d-flex align-items-center">
                <span class="nav-link-icon"><span class="fa fa-chart-pie"></span></span>
                <span class="nav-link-text ps-1"> Theme Examples </span>
              </div>
            </a>
          </li>
        </ul>

        <div class="settings my-3">
          <div class="card shadow-none">
            <div class="card-body alert mb-0" role="alert">
              <div class="text-center">
                <img
                  src="/theme/assets/img/icons/spot-illustrations/navbar-vertical.png"
                  alt=""
                  width="80"
                />
                <p class="fs-11 mt-2">Made with 🔥 by <a href="#!">DPT</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div class="content">
    <nav class="navbar navbar-light navbar-glass navbar-top navbar-expand">
      <button
        class="btn navbar-toggler-humburger-icon navbar-toggler me-1 me-sm-3"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarVerticalCollapse"
        aria-controls="navbarVerticalCollapse"
        aria-expanded="false"
        aria-label="Toggle Navigation"
      >
        <span class="navbar-toggle-icon"><span class="toggle-line"></span></span>
      </button>

      <ul class="navbar-nav align-items-center d-none d-lg-block"></ul>
      <ul class="navbar-nav navbar-nav-icons ms-auto flex-row align-items-center">
        <li class="nav-item ps-2 pe-0">
          <div class="dropdown theme-control-dropdown">
            <a
              class="nav-link d-flex align-items-center dropdown-toggle fa-icon-wait fs-9 pe-1 py-0"
              href="#"
              role="button"
              id="themeSwitchDropdown"
              data-bs-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              ><span
                class="fas fa-sun fs-7"
                data-fa-transform="shrink-2"
                data-theme-dropdown-toggle-icon="light"
              ></span
              ><span
                class="fas fa-moon fs-7"
                data-fa-transform="shrink-3"
                data-theme-dropdown-toggle-icon="dark"
              ></span
              ><span
                class="fas fa-adjust fs-7"
                data-fa-transform="shrink-2"
                data-theme-dropdown-toggle-icon="auto"
              ></span
            ></a>
            <div
              class="dropdown-menu dropdown-menu-end dropdown-caret border py-0 mt-3"
              aria-labelledby="themeSwitchDropdown"
            >
              <div class="bg-white dark__bg-1000 rounded-2 py-2">
                <button
                  class="dropdown-item d-flex align-items-center gap-2"
                  type="button"
                  value="light"
                  data-theme-control="theme"
                >
                  <span class="fas fa-sun"></span>Light<span
                    class="fas fa-check dropdown-check-icon ms-auto text-600"
                  ></span>
                </button>
                <button
                  class="dropdown-item d-flex align-items-center gap-2"
                  type="button"
                  value="dark"
                  data-theme-control="theme"
                >
                  <span class="fas fa-moon" data-fa-transform=""></span>Dark<span
                    class="fas fa-check dropdown-check-icon ms-auto text-600"
                  ></span>
                </button>
                <button
                  class="dropdown-item d-flex align-items-center gap-2"
                  type="button"
                  value="auto"
                  data-theme-control="theme"
                >
                  <span class="fas fa-adjust" data-fa-transform=""></span>Auto<span
                    class="fas fa-check dropdown-check-icon ms-auto text-600"
                  ></span>
                </button>
              </div>
            </div>
          </div>
        </li>

        <li class="nav-item dropdown">
          <a
            class="nav-link pe-0 ps-2"
            id="navbarDropdownUser"
            role="button"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <div class="avatar avatar-xl">
              <!-- <img class="rounded-circle" src="/me/avatar" alt="" /> -->
            </div>
          </a>
          <div
            class="dropdown-menu dropdown-caret dropdown-caret dropdown-menu-end py-0"
            aria-labelledby="navbarDropdownUser"
          >
            <div class="bg-white dark__bg-1000 rounded-2 py-2">
              <a class="dropdown-item fw-bold" href="#!"
                ><span>Krzysztof Szumny <br /><small><EMAIL></small></span></a
              >

              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="/logout">Logout</a>
            </div>
          </div>
        </li>
      </ul>
    </nav>

    <NotificationCenter />
    <RouterView />

    <footer class="footer">
      <div class="row g-0 justify-content-between fs-10 mt-4 mb-3">
        <div class="col-12 col-sm-auto text-center">
          <p class="mb-0 text-600">
            build version: local | build timestamp: - | created using Falcon Boostrap Theme:
            <span class="d-none d-sm-inline-block"> </span><br class="d-sm-none" />
            2024 &copy; <a href="https://themewagon.com">Themewagon</a>
          </p>
        </div>
        <div class="col-12 col-sm-auto text-center">
          <p class="mb-0 text-600">Theme version v3.20.0</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
.nav-item a {
  color: var(--falcon-navbar-vertical-default-link-color);
}

:root[data-env='prod'][data-bs-theme='dark'] .dataoffice-logo {
  content: url('/static/logo-dark_prod.png');
}

:root[data-env='prod'][data-bs-theme='light'] .dataoffice-logo {
  content: url('/static/logo-light_prod.png');
}

:root[data-env='dev'][data-bs-theme='dark'] .dataoffice-logo {
  content: url('/static/logo-dark_dev.png');
}

:root[data-env='dev'][data-bs-theme='light'] .dataoffice-logo {
  content: url('/static/logo-light_dev.png');
}

:root[data-env='local'][data-bs-theme='dark'] .dataoffice-logo {
  content: url('/static/logo-dark_local.png');
}

:root[data-env='local'][data-bs-theme='light'] .dataoffice-logo {
  content: url('/static/logo-light_local.png');
}

@media (width >= 1200px) {
  .logo-container {
    height: 5rem;
  }
}

.logo-container .navbar-brand {
  margin: auto;
}

.env-tag {
  text-transform: uppercase;
  color: grey;
}

.logo-env-tag {
  position: relative;
  right: 18px;
  margin-top: -12px;
  font-size: 12px;
  font-family: var(--falcon-font-sans-serif);
  font-weight: bold;
}

.logo-env-tag .vue {
  color: rgb(68 186 136);
  font-weight: 400;
  font-size: 10px;
}

:root[data-bs-theme='light'] .logo-env-tag .vue {
  font-weight: bold;
}

:root[data-env='prod'] .env-tag {
  color: #e63757;
}

:root[data-env='dev'] .env-tag {
  color: #2c7be5;
}

:root[data-env='local'] .env-tag {
  color: #8c51ea;
}

:root[data-env='prod'] .env-tag::before {
  content: 'prod';
}

:root[data-env='dev'] .env-tag::before {
  content: 'dev';
}

:root[data-env='local'] .env-tag::before {
  content: 'local';
}

.content .navbar {
  height: 5rem;
}
</style>
