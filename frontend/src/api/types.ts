import type { Source } from '@/domain/types'

export type StatusState =
  | 'CONFIGURED'
  | 'DELETED'
  | 'DISABLED'
  | 'FAILED'
  | 'FINISHED'
  | 'MANUALLY_BLOCKED'
  | 'MANUALLY_UNBLOCKED'
  | 'SCHEDULED'
  | 'STARTED'
  | 'STOPPED'
  | 'UNCONFIGURED'

export interface ClientStatusResponse {
  organization_id: string
  source: string
  state: StatusState
  created_at: string
  updated_at: string
  consecutive_failed_scrape_count: number
  last_success_timestamp: string | null
  last_fail_timestamp: string | null
  last_fail_reason: string | null
  user_id: string
  last_origin: string | null
  last_operation_id: string | null
  last_reports: Array<Report | ReportPlaceholder>
  organization_name: string
  user_email: string
  version: number
}

export interface Report {
  id: number
  studio_id: number
  organization_id: string | null
  source: string
  file_path_raw: string
  original_name: string
  portal: string
  date_from: string | null
  date_to: string | null
  no_data: boolean
  upload_date: string
  state: string
  upload_type: string
}
export interface ReportPlaceholder {
  date_from: string
  date_to: string
  state: 'NOT_UPLOADED_YET'
}

export type ManagedPartnerStatusResponse = ClientStatusResponse

export interface MeResponse {
  '@odata.context': string
  businessPhones: string[]
  displayName: string
  givenName: string
  jobTitle: string
  mail: string
  mobilePhone: string | null
  officeLocation: string | null
  preferredLanguage: string | null
  surname: string
  userPrincipalName: string
  id: string
}

export type OrganizationsReportCoverageResponse = Array<{
  organizationId: string
  organizationName: string
  coverage: Record<Source, Array<{ dateFrom: string; dateTo: string }>>
}>
