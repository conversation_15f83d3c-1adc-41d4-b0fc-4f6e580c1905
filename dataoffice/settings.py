from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # url that Dataoffice is served on
    dataoffice_url: str = "http://localhost:8666"

    # credentials for the Entra App Registration that allows Dataoffice to do auth
    auth_client_id: str
    auth_tenant_id: str
    auth_client_secret: str

    # secret key used for signing session cookies; should be a long random string
    cookie_secret_key: str

    # url and api keys for the services that Dataoffice depends on
    pipeline_manager_url: str
    pipeline_manager_key: str

    user_service_url: str
    user_service_key: str

    dataset_manager_url: str
    dataset_manager_key: str

    report_service_url: str
    report_service_key: str

    scraper_service_url: str
    scraper_service_key: str

    # url where cloud-reports publishes execution reports
    cloud_reports_report_url: str

    default_connection_timeout: int = 10

    build_version: str = "local"
    build_timestamp: str = "-"

    env: str = "local"


_settings: Settings | None = None


def settings():
    # having settings being lazy loaded makes it easier to swap
    # out the settings object for testing.
    # Instantiating Settings should fail if some of the required
    # vars are not defined, but we want tests to be able to inject their own settings.
    global _settings
    if _settings is None:
        _settings = Settings(_env_file=".env")  # type: ignore
    return _settings
