<template>
  <div class="status-tooltip">
    <template v-for="(section, sectionIndex) in sections" :key="sectionIndex">
      <div
        v-for="(row, rowIndex) in section.filter(
          ({ value }) => value !== undefined && value !== null,
        )"
        :key="rowIndex"
        class="tooltip-row"
      >
        <span class="tooltip-label">{{ row.label }}:</span>
        <span class="tooltip-value">{{ row.value }}</span>
      </div>
      <div v-if="sectionIndex < sections.length - 1" class="tooltip-divider"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
type Row = { label: string; value: string | number | null }
type Section = Row[]
defineProps<{ sections: Section[] }>()
</script>

<style scoped>
.status-tooltip {
  position: absolute;
  background-color: rgb(0 0 0 / 85%);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  z-index: var(--z-index-status-tooltip);
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  min-width: 180px;
  width: max-content;
  max-width: 400px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
  margin-bottom: 8px;
}

.tooltip-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
}

.tooltip-row:last-child {
  margin-bottom: 0;
}

.tooltip-label {
  color: rgb(255 255 255 / 80%);
  font-weight: 500;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 100px;
  text-align: left;
}

.tooltip-value {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  text-align: left;
}

.tooltip-section {
  margin-top: 8px;
}

.tooltip-divider {
  height: 1px;
  background-color: rgb(255 255 255 / 20%);
  margin: 8px 0;
}
</style>
