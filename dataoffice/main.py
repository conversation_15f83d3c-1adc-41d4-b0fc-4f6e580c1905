import os
from pathlib import Path
from traceback import format_exception

from dataoffice import auth, sidebar
from dataoffice.api import (
    clients_statuses,
    managed_partners_report_coverage,
    managed_partners_statuses,
)
from dataoffice.api.api_tags import tags_metadata
from dataoffice.services.health import get_services_health, get_uptime
from dataoffice.settings import settings
from dataoffice.tab import Tab
from dataoffice.tabs.cloud_reports import cloud_reports
from dataoffice.tabs.data_health import data_health
from dataoffice.tabs.index import index
from dataoffice.tabs.sample_tab import sample_tab
from dataoffice.tabs.scraper_operation_history import scraper_operation_history
from dataoffice.tabs.scraper_statuses import scraper_statuses
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware

app = FastAPI(title="Dataoffice", version="0.1.0", tags_metadata=tags_metadata)

if settings().env == "local":
    # Configure CORS for local development of the frontend
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:5175"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.add_middleware(SessionMiddleware, secret_key=settings().cookie_secret_key)

app.mount("/static", StaticFiles(directory="static"), name="static")

app.include_router(clients_statuses.router)
app.include_router(managed_partners_statuses.router)
app.include_router(managed_partners_report_coverage.router)

_sidebar_config: list[Tab | sidebar.Item] = [
    index.tab,
    data_health.tab,
    cloud_reports.tab,
    scraper_statuses.tab,
    scraper_operation_history.tab,
    sidebar.Item(
        "All Clients",
        "/new/clients-statuses",
        "Status Grids",
        "text-900 fs-9 bi-grid-3x3",
    ),
    sidebar.Item(
        "Managed Partners",
        "/new/managed-partners-statuses",
        "Status Grids",
        "text-900 fs-9 bi-grid-3x3",
    ),
    sidebar.Item(
        "Monthly Coverage",
        "/new/managed-partners-monthly-coverage",
        "Status Grids",
        "text-900 fs-9 bi-grid-3x3",
    ),
    sidebar.Item(
        "Grafana",
        "https://grafana.indiebi.dev/",
        "Monitoring",
        "fa fa-chart-line text-info",
        "DEV cluster",
    ),
    sidebar.Item(
        "Grafana",
        "https://grafana.indiebi.com/",
        "Monitoring",
        "fa fa-chart-line text-danger",
        "PROD cluster",
    ),
    sidebar.Item(
        "Grafana",
        "https://app-grafana-core-indiebi.azurewebsites.net/",
        "Monitoring",
        "fa fa-chart-area",
        "CORE",
    ),
    sidebar.Item(
        "Elastic/Kibana",
        "https://indiebi.kb.westeurope.azure.elastic-cloud.com:9243",
        "Monitoring",
        "fa fa-wave-square",
    ),
    sidebar.Item(
        "Elastic",
        "https://indiebi.kb.westeurope.azure.elastic-cloud.com:9243/s/dpt/app/r/s/Y5inf",
        "Monitoring",
        "fa fa-chart-pie",
        "PowerBI Refresh Dashboard",
    ),
    sidebar.Item(
        "Backoffice",
        "https://backoffice.indiebi.dev",
        "Useful links",
        "fa fa-link text-info",
        "DEV",
    ),
    sidebar.Item(
        "Backoffice",
        "https://backoffice.indiebi.com",
        "Useful links",
        "fa fa-link text-danger",
        "PROD",
    ),
    sidebar.Item(
        "DPT Confluence",
        "https://indiebi.atlassian.net/wiki/spaces/DPT/pages/478216194/Data+Platform+Team+Intro",
        "Useful links",
        "fa fa-link",
    ),
    sidebar.Item(
        "DPT Dev Docs",
        "https://bluebrick.gitlab.io/indiebi/data-platform-team/docs/",
        "Useful links",
        "fa fa-link",
    ),
    sidebar.Item(
        "Login Links",
        "/new/",
        "Useful links",
        "fa fa-link",
    ),
    sidebar.Item(
        "Theme Examples", "/new/theme/index.html", "Sample pages", "fa fa-chart-pie"
    ),
]

app.include_router(auth.router)

for item in _sidebar_config:
    if isinstance(item, Tab):
        app.include_router(item.routes)
        item = item.as_sidebar_item()

    sidebar.add_item(item)

app.include_router(sample_tab.tab.routes)


@app.exception_handler(auth.LoginRequiredException)
async def login_required_exception_handler(
    request: Request, exc: auth.LoginRequiredException
):
    return auth.redirect_to_login(request)


@app.exception_handler(404)
def not_found_exception_handler(request: Request, exc: Exception):
    return JSONResponse({"message": "Not Found", "url": str(request.url)}, 404)


_error_tab = Tab(title="Error", template="error.html.j2", prefix="", section="")


@app.exception_handler(500)
def server_error_exception_handler(request: Request, exc: Exception):
    error_details = "\n".join(format_exception(exc))
    error_message = f"{type(exc).__name__}: {exc}"

    return _error_tab.render(
        (request, auth.active_session(request), False),
        {"error_details": error_details, "error_message": error_message},
    )


@app.get("/health")
async def health(request: Request):
    return {
        "message": "ok",
        "session": request.session,
        "instance": os.uname(),
        "version": settings().build_version,
        "timestamp": settings().build_timestamp,
        "uptime": get_uptime(),
        "services": await get_services_health(),
    }


@app.get("/new/{path:path}")
async def serve_vue_app(
    request: Request, path: str, session: auth.ActiveSessionDependency
):
    """Serve the Vue.js application for all /new/* paths."""
    INDEX_PATH = Path("static/frontend/index.html")

    if not INDEX_PATH.exists():
        return JSONResponse(
            status_code=404, content={"error": "Vue.js application not built"}
        )

    # If path is empty, return index.html
    if path == "":
        return FileResponse(INDEX_PATH)

    # Check if the requested path is a static file
    static_file_path = Path("static/frontend") / path
    if static_file_path.exists() and static_file_path.is_file():
        return FileResponse(static_file_path)

    # For all other paths, return index.html to support client-side routing
    return FileResponse(INDEX_PATH)
