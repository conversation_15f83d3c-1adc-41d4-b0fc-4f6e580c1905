import time
from typing import Annotated, Dict

import httpx
from dataoffice.api.utils import strip_managed_partner_suffix
from dataoffice.connectors import report_service, scrapers_service, user_service
from dataoffice.services.types import ClientStatus, ReportPlaceholder
from fastapi import Depends


class UsersCache:
    """Class to manage user cache."""

    CACHE_TTL = 24 * 60 * 60  # 24 hours in seconds

    def __init__(self):
        self.users = {}
        self.last_cache_update = 0

    @property
    def is_cache_valid(self):
        return time.time() - self.last_cache_update < self.CACHE_TTL and bool(
            self.users
        )


_users_cache = UsersCache()


def get_users_cache() -> UsersCache:
    return _users_cache


UsersCacheDependency = Annotated[UsersCache, Depends(get_users_cache)]


async def _get_user_data(
    user_id: str, users: Dict[str, user_service.User]
) -> user_service.User:
    """Get user data from cache or fetch it if not available."""
    user = users.get(user_id)
    if user:
        return user

    try:
        user = await user_service.get_user(user_id)
    except httpx.HTTPStatusError as e:
        if e.response is not None and e.response.status_code == 404:
            return user_service.User.get_deleted_user()
        else:
            raise e

    users[user_id] = user
    return user


async def _get_all_users(cache: UsersCache) -> Dict[str, user_service.User]:
    """Fetch all users and store them in a cache."""

    if cache.is_cache_valid:
        return cache.users

    # Fetch all users with pagination
    all_users = []
    offset = 0
    limit = 1000

    while True:
        users = await user_service.search_users("", offset=offset, limit=limit)
        if not users:
            break

        all_users.extend(users)
        if len(users) < limit:
            break

        offset += limit

    # Update cache only if we found some users
    if all_users:
        cache.users = {user.id: user for user in all_users}
        cache.last_cache_update = time.time()

    return cache.users


class ReportsCache:
    CACHE_TTL = 24 * 60 * 60  # 24 hours in seconds
    FINAL_STATES = {"CONVERTED", "FAILED", "DELETED"}

    def __init__(self):
        self.reports: dict[int, report_service.Report] = {}
        self.last_cache_update = 0

    @property
    def is_cache_valid(self):
        return time.time() - self.last_cache_update < self.CACHE_TTL and bool(
            self.reports
        )

    def get_report(self, report_id):
        report = self.reports.get(report_id)
        if report and report.state in self.FINAL_STATES:
            return report
        return None

    def set_reports(self, reports):
        for report in reports:
            if getattr(report, "state", None) not in self.FINAL_STATES:
                self.reports[report.id] = report
        self.last_cache_update = int(time.time())


_reports_cache = ReportsCache()


def get_reports_cache() -> ReportsCache:
    return _reports_cache


ReportsCacheDependency = Annotated[ReportsCache, Depends(get_reports_cache)]


async def _get_reports_by_ids(
    report_ids: list[int], cache: "ReportsCache"
) -> dict[int, report_service.Report]:
    result = {}
    ids_to_fetch = []
    for report_id in report_ids:
        report = cache.get_report(report_id)
        if report:
            result[report_id] = report
        else:
            ids_to_fetch.append(report_id)
    if ids_to_fetch:
        reports_response = await report_service.get_reports(
            report_service.ReportSearchParams(
                ids=ids_to_fetch,
                limit=1000,
                sort_by=["-upload_date"],
            )
        )
        for report in reports_response.data:
            if report.state in ReportsCache.FINAL_STATES:
                cache.reports[report.id] = report
            result[report.id] = report
    return result


async def get_clients_statuses_service(
    users_cache: UsersCache,
    reports_cache: ReportsCache,
    managed_partners_only: bool = False,
) -> list[ClientStatus]:
    scraper_statuses: list[
        scrapers_service.ScraperStatusProjection
    ] = await scrapers_service.get_scraper_statuses()
    users = await _get_all_users(users_cache)

    result: list[ClientStatus] = []
    for scraper_status in scraper_statuses:
        if not scraper_status.user_id.startswith("u-"):
            continue

        user = await _get_user_data(scraper_status.user_id, users)
        org_name = user.company_name or ""
        org_id = scraper_status.organization_id

        if managed_partners_only and not is_managed_partner(org_name, org_id):
            continue

        reports: list[report_service.Report | ReportPlaceholder] = []
        reports_by_date_range: dict[str, report_service.Report] = {}

        if scraper_status.last_report_ids:
            reports_dict = await _get_reports_by_ids(
                scraper_status.last_report_ids, reports_cache
            )
            for report in reports_dict.values():
                reports_by_date_range[f"{report.date_from}-{report.date_to}"] = report

        if scraper_status.last_scrape_date_ranges:
            for date_range in scraper_status.last_scrape_date_ranges:
                key = f"{date_range.date_from}-{date_range.date_to}"
                if key in reports_by_date_range:
                    reports.append(reports_by_date_range[key])
                else:
                    reports.append(
                        ReportPlaceholder(
                            date_from=date_range.date_from,
                            date_to=date_range.date_to,
                            state="NOT_UPLOADED_YET",
                        )
                    )

        result.append(
            ClientStatus(
                **{
                    **scraper_status.model_dump(),
                    "organization_name": strip_managed_partner_suffix(org_name),
                    "user_email": user.email or "",
                    "last_reports": reports,
                }
            )
        )

    return result


def is_managed_partner(org_name: str, org_id: str) -> bool:
    return "Managed" in org_name or org_id in ORG_IDS_OF_ADDITIONAL_MANAGED_CLIENTS


ORG_IDS_OF_ADDITIONAL_MANAGED_CLIENTS = [
    "o-AL6Tv7",  # SUPERHOT
    "o-LIYf78",  # Raw Fury
    "o-X1fjLv",  # Secret Mode
    "o-PAmMTX",  # Red Hook Studios
    "o-bQLihp",  # Relic Entertainment, Inc
]


async def fill_clients_with_organization_name_dict(
    scraper_statuses: list[dict], users_cache: UsersCache
) -> list[dict]:
    users = await _get_all_users(users_cache)
    result = []

    for status in scraper_statuses:
        if status["user_id"].startswith("u-"):
            user = await _get_user_data(status["user_id"], users)
            status.update(
                {
                    "organization_name": user.company_name or "",
                    "user_email": user.email or "",
                }
            )
        result.append(status)

    return result
