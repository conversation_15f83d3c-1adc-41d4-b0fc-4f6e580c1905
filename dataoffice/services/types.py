from datetime import datetime
from typing import Literal

from dataoffice.connectors.report_service import Report
from dataoffice.connectors.scrapers_service import ScraperBinaryStatus
from pydantic import BaseModel


class DateRange(BaseModel):
    date_from: str
    date_to: str
    days_in_range: int


class ReportPlaceholder(BaseModel):
    date_from: str
    date_to: str
    state: Literal["NOT_UPLOADED_YET"]


class ClientStatus(BaseModel):
    organization_id: str
    user_id: str
    source: str
    state: ScraperBinaryStatus
    created_at: datetime
    updated_at: datetime
    consecutive_failed_scrape_count: int
    last_success_timestamp: datetime | None
    last_fail_timestamp: datetime | None
    last_fail_reason: str | None
    last_origin: str | None
    last_operation_id: str | None
    last_reports: list[Report | ReportPlaceholder] | None
    organization_name: str
    user_email: str
    version: int
