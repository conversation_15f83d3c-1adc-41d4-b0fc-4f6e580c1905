from dataoffice.api.api_tags import ApiTag
from dataoffice.services.clients_statuses_service import (
    ReportsCacheDependency,
    UsersCacheDependency,
    get_clients_statuses_service,
)
from dataoffice.services.types import ClientStatus
from fastapi import APIRouter

router = APIRouter(prefix="/api/managed-partners/statuses")


@router.get(
    "/",
    response_model=list[ClientStatus],
    tags=[ApiTag.MANAGED_PARTNERS],
)
async def get_managed_partners_statuses(
    users_cache: UsersCacheDependency,
    reports_cache: ReportsCacheDependency,
) -> list[ClientStatus]:
    return await get_clients_statuses_service(
        users_cache, reports_cache, managed_partners_only=True
    )
