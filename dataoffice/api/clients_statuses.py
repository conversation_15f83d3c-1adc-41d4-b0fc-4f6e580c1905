from dataoffice.services.clients_statuses_service import (
    ReportsCacheDependency,
    UsersCacheDependency,
    get_clients_statuses_service,
)
from dataoffice.services.types import ClientStatus
from fastapi import APIRouter

router = APIRouter(prefix="/api/clients/statuses")


@router.get(
    "/",
    response_model=list[ClientStatus],
    tags=[],
)
async def get_clients_statuses(
    users_cache: UsersCacheDependency,
    reports_cache: ReportsCacheDependency,
) -> list[ClientStatus]:
    return await get_clients_statuses_service(users_cache, reports_cache)
